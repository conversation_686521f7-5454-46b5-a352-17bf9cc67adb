// @ts-check
import * as React from 'react';
import { getText } from '../i18n.js';
import { memo } from 'react';
import { Building, PlacedBuilding, BuildingInteraction } from '../Interfaces.js';
import { BUILDINGS } from '../enums/building_enums.js';
import { ItemIcon } from './common.js';
import { useRootStore } from '../stores/rootStore.js';
import { useBuildingStore } from '../stores/buildingStore.js';
import { GRID_HEIGHT, GRID_WIDTH } from '../settings.js';
import { MacOSModal } from './WindowManagement/MacOSModal';



const BuildingItem = (props: {
    building: Building
}) => {
    if (props.building.hideInCraftList) {
        return;
    }
    
    const { selectedBuildingToPlace, setSelectedBuildingToPlace } = useBuildingStore();
    const quantityMap = useRootStore(state => state.quantityMap);
    // const { selectBuilding } = useBuildingSystem();
    // const { selectedBuildingToPlace } = useBuildingSystem();
    const isSelected = selectedBuildingToPlace?.id === props.building.id;

    return (
        <div
            className={`building-item ${isSelected ? 'selected' : ''}`}
            onClick={() => setSelectedBuildingToPlace(props.building)}
        >
            <div className="building-icon">
                <ItemIcon itemDef={props.building} />
            </div>
            <div className="building-info">
                <div className="building-name">{props.building.name}</div>
                <div className="building-desc">{props.building.description}</div>
                <div className="building-resources">
                    {props.building.resources.map((res, index) => (
                        <span key={index}>{quantityMap[res.itemDef.id] >= res.quantity ? '✅' : '❌'} {res.quantity} {res.itemDef.name}</span>
                    )).reduce((prev, curr) => prev === null ? curr : <>{prev}, {curr}</>, null)}
                </div>
            </div>
        </div>
    );
};


const PlacedBuildings = memo(({ inBuildMode, isDragging } : {
    inBuildMode: boolean,
    isDragging: boolean
}) => {
    console.log('PlacedBuildings rendered');

    const placedBuildingsInAllRegions = useRootStore(state => state.placedBuildingsInAllRegions);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const removeBuildingFromRegion = useRootStore(state => state.removeBuildingFromRegion);


    const placedBuildingsMap = placedBuildingsInAllRegions[currRegionIndex];

    const [interactingPlacedBuilding, setInteractingPlacedBuilding] = React.useState(null);
    const interactionModalId = React.useRef(`building-interaction-modal-${Date.now()}`);

    // Clean up the interaction modal when component unmounts
    React.useEffect(() => {
        return () => {
            // Ensure any open modal is closed when component unmounts
            if (interactingPlacedBuilding) {
                console.log('Component unmounting, cleaning up interaction modal');
                setInteractingPlacedBuilding(null);
            }
        };
    }, []);

    const cellSize = 40;

    const placedBuildingUuids = new Set();
    const placedBuildings: PlacedBuilding[] = [];
    placedBuildingsMap.forEach((placedBuilding) => {
        if (placedBuildingUuids.has(placedBuilding.id)) return;
        placedBuildingUuids.add(placedBuilding.id);
        placedBuildings.push(placedBuilding);
    });

    placedBuildingsMap.forEach((placedBuilding) => {
        if (placedBuildingUuids.has(placedBuilding.id)) return;

        placedBuildings.push(
        );
    });


    console.log('PlacedBuildings rendered, interactingPlacedBuilding:', interactingPlacedBuilding);


    return (
        <>
            {placedBuildings.map((placedBuilding) => (
                <div
                    key={`building-${placedBuilding.id}`}
                    className="placed-building"
                    style={{
                        position: 'absolute',
                        left: `${placedBuilding.x * cellSize}px`,
                        top: `${placedBuilding.y * cellSize}px`,
                        width: `${placedBuilding.buildingDef.width * cellSize}px`,
                        height: `${placedBuilding.buildingDef.height * cellSize}px`,
                        // zIndex: inBuildMode ? 0 : 10,
                        pointerEvents: isDragging ? 'none' : 'auto'
                    }}
                    onClick={inBuildMode || isDragging ? undefined : () => setInteractingPlacedBuilding(placedBuilding)}
                >
                    <ItemIcon itemDef={placedBuilding.buildingDef} uuid={placedBuilding.id} />
                </div>
            ))}

            {interactingPlacedBuilding && (
                <MacOSModal
                    title={interactingPlacedBuilding.buildingDef.name}
                    isOpen={true}
                    onClose={() => setInteractingPlacedBuilding(null)}
                    initialSize={{ width: 400, height: 300 }}
                    portalId={interactionModalId.current}
                >
                    <div className="building-interactions">
                        {/* Show fire duration if this is a fire pit and it's lit */}
                        {interactingPlacedBuilding.buildingDef.id === 'FIRE_PIT' && interactingPlacedBuilding.isLit && (
                            <div className="fire-status">
                                <div className="fire-icon">🔥</div>
                                <div className="fire-info">
                                    <div>{getText('Fire is burning')}</div>
                                    <div className="fire-duration">{getText('Remaining time')}: {interactingPlacedBuilding.fireDuration} {getText('minutes')}</div>
                                </div>
                            </div>
                        )}

                        {interactingPlacedBuilding.buildingDef.interactions.map((interaction: BuildingInteraction) => {
                            // Check if interaction has a condition and if it passes
                            if (interaction.condition && !interaction.condition(interactingPlacedBuilding)) {
                                return null;
                            }

                            return (
                                <button
                                    key={interaction.id}
                                    className="interaction-button"
                                    onClick={() => {
                                        if (interaction.id == 'chop') {
                                            removeBuildingFromRegion(interactingPlacedBuilding.id);
                                        } else if (interaction.id == 'clean_stone') {
                                            removeBuildingFromRegion(interactingPlacedBuilding.id);
                                        } else {
                                            interaction.action(interactingPlacedBuilding);
                                        }
                                        setInteractingPlacedBuilding(null);
                                    }}
                                >
                                    {interaction.icon} {interaction.name}
                                </button>
                            );
                        })}
                    </div>
                </MacOSModal>
            )}
        </>
    );
});

const BuildingPreview = React.memo(() => {
    const { selectedBuildingToPlace, hoverPosition, isValidPlacement } = useBuildingStore();

    if (!selectedBuildingToPlace || !hoverPosition) return null;

    // Remove console.log to improve performance
    // console.log(`BuildingPreview rendered with x: ${hoverPosition.x}, y: ${hoverPosition.y}, building: ${selectedBuildingToPlace}`);

    const cellPx = 40;

    return (
        <div
            className={`building-preview ${isValidPlacement ? 'valid-placement' : 'invalid-placement'}`}
            style={{
                position: 'absolute',
                left: `${hoverPosition.x * cellPx}px`,
                top: `${hoverPosition.y * cellPx}px`,
                width: `${selectedBuildingToPlace.width * cellPx}px`,
                height: `${selectedBuildingToPlace.height * cellPx}px`,
                zIndex: 5
            }}
        >
            <ItemIcon itemDef={selectedBuildingToPlace} />
        </div>
    );
});

// TODO: Add random trees and rocks
const GridCell = React.memo(({ x, y } : {
    x: number,
    y: number,
}) => {
    // Remove console.log to reduce performance overhead
    // console.log(`GridCell rendered with x: ${x}, y: ${y}`);

    const { placeBuildingAtRegion, currRegionIndex, placedBuildingsInAllRegions, biomesList } = useRootStore();

    const selectedBuildingToPlace = useBuildingStore(state => state.selectedBuildingToPlace);
    const setHoverPosition = useBuildingStore(state => state.setHoverPosition);
    const setIsValidPlacement = useBuildingStore(state => state.setIsValidPlacement);

    const placedBuildingsMap = placedBuildingsInAllRegions[currRegionIndex];

    // Use useCallback to memoize event handlers to prevent unnecessary re-renders
    const handleClick = React.useCallback(() => {
        if (selectedBuildingToPlace) {
            placeBuildingAtRegion(x, y, selectedBuildingToPlace);
        }
    }, [x, y, selectedBuildingToPlace, placeBuildingAtRegion]);

    const handleMouseEnter = React.useCallback(() => {
        if (selectedBuildingToPlace) {
            setHoverPosition({ x, y });
            setIsValidPlacement(canPlaceBuildingAt(x, y, selectedBuildingToPlace, placedBuildingsMap));
        }
    }, [x, y, selectedBuildingToPlace, placedBuildingsMap, setHoverPosition,
        setIsValidPlacement
    ]);

    // Only render the cell with event handlers when in edit mode
    // This prevents unnecessary event handling when not in edit mode
    return (
        <div
            className="grid-cell"
            style={{
                backgroundColor: biomesList[currRegionIndex] == 'BEACH' ? 'rgb(255, 240, 180)' : 'rgb(195 214 87)',
            }}
            data-x={x}
            data-y={y}
            onClick={selectedBuildingToPlace ? handleClick : undefined}
            onMouseEnter={selectedBuildingToPlace ? handleMouseEnter : undefined}
            // style={(x % 2 === 1 && y % 2 === 0) || (x % 2 === 0 && y % 2 === 1) ? {backgroundColor: 'rgb(31, 31, 31)'} : null}
        />
    );
}, (prevProps, nextProps) => {
    // Custom comparison function for memo
    // Only re-render if x or y changes
    return prevProps.x === nextProps.x && prevProps.y === nextProps.y;
});

const GridCells = React.memo(() => {
    console.log('GridCells rendered');

    // Use useMemo to prevent recreating the cells array on every render
    const cells = React.useMemo(() => {
        const cellsArray = [];
        for (let y = 0; y < GRID_WIDTH; y++) {
            for (let x = 0; x < GRID_HEIGHT; x++) {
                cellsArray.push(
                    <GridCell key={`${x}-${y}`} x={x} y={y} />
                );
            }
        }
        return cellsArray;
    }, []); // Only recalculate when grid dimensions change

    return <>{cells}</>;
});

const BuildingGrid = () => {
    const selectedBuildingToPlace = useBuildingStore(state => state.selectedBuildingToPlace);

    const [zoom, setZoom] = React.useState(0.4);
    const [position, setPosition] = React.useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = React.useState(false);
    const [dragStart, setDragStart] = React.useState({ x: 0, y: 0 });
    const gridRef = React.useRef(null);

    // Handle mouse wheel for zooming
    const handleWheel = React.useCallback((e: WheelEvent) => {
        e.preventDefault();
        // Adjust zoom based on wheel direction
        const newZoom = e.deltaY < 0
            ? Math.min(zoom + 0.1, 2) // Zoom in (max 2x)
            : Math.max(zoom - 0.1, 0.2); // Zoom out (min 0.2x)
        setZoom(newZoom);
    }, [zoom]);

    // Handle mouse down for panning
    const handleMouseDown = React.useCallback((e: React.MouseEvent) => {
        // Only enable dragging when not in edit mode
        if (!selectedBuildingToPlace) {
            setIsDragging(true);
            setDragStart({ x: e.clientX, y: e.clientY });
        }
    }, [selectedBuildingToPlace]);

    // Handle mouse move for panning
    const handleMouseMove = React.useCallback((e: React.MouseEvent) => {
        if (isDragging && !selectedBuildingToPlace) {
            const dx = e.clientX - dragStart.x;
            const dy = e.clientY - dragStart.y;
            setPosition({
                x: position.x + dx,
                y: position.y + dy
            });
            setDragStart({ x: e.clientX, y: e.clientY });
        }
    }, [isDragging, dragStart, position, selectedBuildingToPlace]);

    // Handle mouse up to stop panning
    const handleMouseUp = React.useCallback(() => {
        setIsDragging(false);
    }, []);

    // Add and remove event listeners
    React.useEffect(() => {
        const grid = gridRef.current;
        if (grid) {
            grid.addEventListener('wheel', handleWheel, { passive: false });
        }

        // Clean up event listeners
        return () => {
            if (grid) {
                grid.removeEventListener('wheel', handleWheel);
            }
        };
    }, [handleWheel]);

    // // Set cursor style based on mode
    // const cursorStyle = selectedBuildingToPlace
    //     ? 'default'
    //     : isDragging ? 'grabbing' : 'grab';

    return (
        <div
            ref={gridRef}
            className="building-grid"
            style={{
                transform: `scale(${zoom})`,
                // cursor: cursorStyle,
                left: `${position.x}px`,
                top: `${position.y}px`,
                transition: isDragging ? 'none' : 'transform 0.2s ease',
                gridTemplateColumns: `repeat(${GRID_WIDTH}, 40px)`,
                gridTemplateRows: `repeat(${GRID_HEIGHT}, 40px)`,
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
        >
            <PlacedBuildings inBuildMode={selectedBuildingToPlace != null} isDragging={isDragging} />

            <GridCells/>

            {/* Show building preview when hovering */}
            <BuildingPreview/>
        </div>
    );
};

const CancelBuildBtn = () => {
    const selectedBuildingToPlace = useBuildingStore(state => state.selectedBuildingToPlace);
    const setSelectedBuildingToPlace = useBuildingStore(state => state.setSelectedBuildingToPlace);

    return (
        <>
            {selectedBuildingToPlace && (
                <div className="cancel-placement-button" onClick={() => setSelectedBuildingToPlace(null)}>
                    {getText('Exit Building Mode')}
                </div>)
            }
        </>
    );
};


export const BuildingSystemRoot = () => {
    const [isFolded, setIsFolded] = React.useState(false);

    const toggleFold = () => {
        setIsFolded(!isFolded);
    };

    return (
        <div className="building-root">
            <div className={`build-window-left ${isFolded ? 'folded' : ''}`}>
                <div className='available-buildings-header'>
                    <div style={{marginRight: "auto"}}>
                        <div>{getText('Available Buildings')}</div>
                    </div>
                    <CancelBuildBtn />
                </div>
                <div id="available-buildings-list" className="building-list scrollable-container">
                    {Object.values(BUILDINGS).map(building => (
                        <BuildingItem key={building.id}
                            building={building}
                        />
                    ))}
                </div>
            </div>
            <button 
                className="fold-button" 
                style={{left: isFolded? '0' : '37%'}}
                onClick={toggleFold}
                title={isFolded ? getText('Unfold') : getText('Fold')}
            >
                <img className='fold-btn-img' src = {`images/ui_icons/double_arrow_${isFolded ? "right" : "left"}.svg`} />
            </button>

            <div className="build-window-right">
                {/* <div className="building-grid-header">
                    <h4>{getText('Building Grid')}</h4>
                    <div className="grid-instructions">
                        {selectedBuildingToPlace
                            ? getText('Click to place building')
                            : getText('Use mouse wheel to zoom, drag to pan')}
                    </div>
                </div> */}
                <div className="building-grid-container">
                    <BuildingGrid/>
                </div>
            </div>
        </div>
    );
};

// export function initBuildingSystem(placedBuildingsInCurrentRegion: Map<string, PlacedBuilding>) {
//     // Create container for the building system
//     const buildingDiv = document.querySelector('#Building') || document.body;
//     // const buildingSystemContainer = document.createElement('div');
//     // buildingSystemContainer.id = 'building-system-container';
//     // gameControls.appendChild(buildingSystemContainer);

//     const root = createRoot(buildingDiv);
//     root.render(<BuildingSystemRoot
//         placedBuildingsInCurrentRegion={placedBuildingsInCurrentRegion}
//     />);

//     // root.render(<Stash />);
// }




/**
 * Check if a building can be placed at the specified position
 */
const canPlaceBuildingAt = (
    x: number,
    y: number,
    selectedBuildingToPlace: Building,
    placedBuildingsMap: Map<string, PlacedBuilding>
): boolean => {
    const building = selectedBuildingToPlace;

    // Check if building fits within grid bounds
    if (x + building.width > GRID_WIDTH ||
        y + building.height > GRID_HEIGHT) {
        return false;
    }

    if (placedBuildingsMap) {
        for (let i = x; i < x + building.width; i++) {
            for (let j = y; j < y + building.height; j++) {
                // Check if a cell is already occupied
                if (placedBuildingsMap.has(`${i}_${j}`)) {
                    return false;
                }
            }
        }
    }

    // Remove console.log to improve performance
    // console.log("Building can be placed at x: " + x + ", y: " + y);

    return true;
};




// const BuildingInteractionMenu = ({ placedBuilding, onClose } : {
//     placedBuilding: PlacedBuilding,
//     onClose: () => void
// }) => {
//     const buildingDef = placedBuilding.buildingDef;

//     if (!buildingDef?.interactions) {
//         return null;
//     }

//     return (
//         <MyDialogModal title={buildingDef.name} isOpen={true} >
//             {buildingDef.interactions.map(interaction => (
//                 <button
//                     key={interaction.id}
//                     className="interaction-button"
//                     onClick={() => {
//                         interaction.action(placedBuilding);
//                         onClose();
//                     }}
//                 >
//                     {interaction.icon} {interaction.name}
//                 </button>
//             ))}
//         </MyDialogModal>
//         // <div className="building-interaction-menu" data-building-id={placedBuilding.id}>
//         //     <div className="interaction-header">
//         //         <h3>{buildingDef.name}</h3>
//         //         <span className="close-interaction" onClick={onClose}>&times;</span>
//         //     </div>
//         //     <div className="interaction-body">
//         //         {buildingDef.interactions.map(interaction => (
//         //             <button
//         //                 key={interaction.id}
//         //                 className="interaction-button"
//         //                 onClick={() => {
//         //                     interaction.action(placedBuilding);
//         //                     onClose();
//         //                 }}
//         //             >
//         //                 {interaction.icon} {interaction.name}
//         //             </button>
//         //         ))}
//         //     </div>
//         // </div>
//     );
// };


