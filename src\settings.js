// Settings menu component for mapgen2

import { Languages, setLanguage, currentLanguage, getText } from './i18n.js';
import { TransitionsProbs, Weathers } from './weather.js';
import { v4 as uuidv4 } from 'uuid';

// export const enableFogOfWar = true;
export const enableFogOfWar = false;

export const mapSize = {
    tiny: 1000, //38, 
    small: 1461, //26,
    medium: 2111, //18,
    large: 2968, //12.8,
    huge: 4222, //9,
};

export const GRID_WIDTH = 30;
export const GRID_HEIGHT = 30;

const realSecToGameSecRatio = 6000; // 6 real seconds = 3600 seconds (1 hour) in game
export const realSecToGameMinRatio = realSecToGameSecRatio / 60;

// Add base rates for player stats
export const baseWaterDrainRate = 0.14;
export const baseFoodDrainRate = 0.07;
export const baseEnergyDrainRate = 0.035;
export const basePlayerSpeed = 1;
export const baseSpoilSpeed = 1; // lose 1 freshness per in game minute

export const DEFAULT_INV_SLOT = 40;

// Default settings
const defaultSettings = {
    language: currentLanguage,
    masterVolume: 0.7,
    musicVolume: 0.5,
    sfxVolume: 0.6,
    weatherSoundVolume: 0.5,
    uiSoundVolume: 0.6
};


// Initial inventory items
export const MockedItemStacks = [
    { itemId: "Bark", quantity: 11, uuid: uuidv4() },
    { itemId: "Log", quantity: 11, uuid: uuidv4() },
    { itemId: "Grass", quantity: 15, uuid: uuidv4() },
    { itemId: "Leaf", quantity: 15, uuid: uuidv4() },
    { itemId: "Acorn", quantity: 3, uuid: uuidv4() },
    { itemId: "Flint", quantity: 11, uuid: uuidv4() },
    { itemId: "Stone", quantity: 11, uuid: uuidv4() },
    { itemId: "Cyan_Cod", quantity: 1, uuid: uuidv4() },
    { itemId: "StoneAxe", quantity: 1, uuid: uuidv4() },
    // { itemId: "Juice", quantity: 11, uuid: uuidv4() },
    { itemId: "Apple", quantity: 11, uuid: uuidv4() },
    { itemId: "Honey", quantity: 1, uuid: uuidv4() },
    { itemId: "Crab", quantity: 1, uuid: uuidv4() },
    { itemId: "Manure", quantity: 11, uuid: uuidv4() },
    { itemId: "Sand", quantity: 11, uuid: uuidv4() },
    { itemId: "BoneGlue", quantity: 11, uuid: uuidv4() },
    { itemId: "Beef", quantity: 11, uuid: uuidv4() },
    { itemId: "Water", quantity: 11, uuid: uuidv4() },
    { itemId: "SmallBackpack", quantity: 2, uuid: uuidv4() },
];

// Current settings (initialized with defaults)
let settings = { ...defaultSettings };

/**
 * @type {AudioContext}
 */
let audioContext = null;
/**
 * @type {GainNode}
 */
let masterGainNode = null;
/**
   * @type {GainNode}
   */
let musicGainNode = null;
/**
   * @type {GainNode}
   */
let sfxGainNode = null;
let weatherGainNode = null;
let uiGainNode = null;

/**
 * @type {AudioBufferSourceNode}
 */
let sourceNode = null;

const buffers = {};

// Initialize audio system
export function initAudio() {
    try {
        // Create audio context
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        // Create gain nodes
        masterGainNode = audioContext.createGain();
        musicGainNode = audioContext.createGain();
        sfxGainNode = audioContext.createGain();
        weatherGainNode = audioContext.createGain();
        uiGainNode = audioContext.createGain();
        
        // Connect nodes
        musicGainNode.connect(masterGainNode);
        sfxGainNode.connect(masterGainNode);
        weatherGainNode.connect(masterGainNode);
        uiGainNode.connect(masterGainNode);
        masterGainNode.connect(audioContext.destination);
        
        // Set initial volumes
        setVolume('masterVolume', settings.masterVolume);
        setVolume('musicVolume', settings.musicVolume);
        setVolume('sfxVolume', settings.sfxVolume);
        setVolume('weatherSoundVolume', settings.weatherSoundVolume);
        setVolume('uiSoundVolume', settings.uiSoundVolume);

        for (const [_, w] of Object.entries(Weathers)) {
            if (w.soundPath && !buffers[w.soundPath]) {
                fetch(w.soundPath)
                  .then(response => response.arrayBuffer())
                  .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
                  .then(audioBuffer => {
                        buffers[w.soundPath] = audioBuffer;
                  }) 
            }
        }
        
        console.log('Audio system initialized');
    } catch (e) {
        console.error('Failed to initialize audio system:', e);
    }
}

// Set volume for a specific channel
export function setVolume(channel, value) {
    if (value < 0) value = 0;
    if (value > 1) value = 1;
    
    settings[channel] = value;
    
    // Update gain nodes if audio is initialized
    if (audioContext) {
        switch (channel) {
            case 'masterVolume':
                masterGainNode.gain.value = value;
                break;
            case 'musicVolume':
                musicGainNode.gain.value = value;
                break;
            case 'sfxVolume':
                sfxGainNode.gain.value = value;
                break;
            case 'weatherSoundVolume':
                weatherGainNode.gain.value = value;
                break;
            case 'uiSoundVolume':
                uiGainNode.gain.value = value;
                break;
        }
    }
    
    // Save settings
    saveSettings();
}


const fadeDuration = 0.5; // seconds for fade effects

// Play a sound effect
export function playSFX(url) {
    if (!audioContext) initAudio();
    
    console.log('Playing sound effect:', url);
    
    sourceNode = audioContext.createBufferSource();
    sourceNode.buffer = buffers[url];
    sourceNode.connect(masterGainNode);
            
    // Set up fade in
    const currentTime = audioContext.currentTime;
    masterGainNode.gain.setValueAtTime(0, currentTime);
    masterGainNode.gain.linearRampToValueAtTime(1, currentTime + fadeDuration);

    sourceNode.start(0);


    // fetch(url)
    //     .then(response => response.arrayBuffer())
    //     .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
    //     .then(audioBuffer => {
    //         // stopAudio();
    //         console.log('Playing sound:', url);
    //         sourceNode = audioContext.createBufferSource();
    //         sourceNode.buffer = audioBuffer;
    //         sourceNode.connect(masterGainNode);

                    
    //         // Set up fade in
    //         const currentTime = audioContext.currentTime;
    //         masterGainNode.gain.setValueAtTime(0, currentTime);
    //         masterGainNode.gain.linearRampToValueAtTime(1, currentTime + fadeDuration);

    //         sourceNode.start(0);
    //     })
    //     .catch(e => console.error('Error playing sound:', e));
}

export function stopAudio() {
    if (sourceNode != null) {
        const currentTime = audioContext.currentTime;
        masterGainNode.gain.setValueAtTime(masterGainNode.gain.value, currentTime);
        masterGainNode.gain.linearRampToValueAtTime(0, currentTime + fadeDuration);

        sourceNode.stop(currentTime + fadeDuration);
        sourceNode.onended = () => {
            sourceNode.disconnect();
            sourceNode = null;
        };
    }
}

// Play a UI sound
export function playUISound(url) {
    if (!audioContext) initAudio();
    
    fetch(url)
        .then(response => response.arrayBuffer())
        .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
        .then(audioBuffer => {
            const source = audioContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(uiGainNode);
            source.start(0);
        })
        .catch(e => console.error('Error playing UI sound:', e));
}

// Save settings to localStorage
function saveSettings() {
    try {
        localStorage.setItem('mapgen2_settings', JSON.stringify(settings));
    } catch (e) {
        console.error('Failed to save settings:', e);
    }
}

// Load settings from localStorage
function loadSettings() {
    try {
        const savedSettings = localStorage.getItem('mapgen2_settings');
        if (savedSettings) {
            console.log('Loaded settings:', savedSettings);
            settings = { ...defaultSettings, ...JSON.parse(savedSettings) };
            
            // Apply loaded settings
            if (settings.language !== currentLanguage) {
                setLanguage(settings.language);
            }
            
            if (audioContext) {
                setVolume('masterVolume', settings.masterVolume);
                setVolume('musicVolume', settings.musicVolume);
                setVolume('sfxVolume', settings.sfxVolume);
                setVolume('weatherSoundVolume', settings.weatherSoundVolume);
                setVolume('uiSoundVolume', settings.uiSoundVolume);
            }
        }
    } catch (e) {
        console.error('Failed to load settings:', e);
    }
}

// Create and initialize the settings menu
export function initSettingsMenu() {
    // Load saved settings
    loadSettings();

    
    // Create settings button
    const settingsButton = document.getElementById('settingsButton');
    // settingsButton.innerHTML = '⚙️';
    // settingsButton.title = getText('ui_settings');
    
    // Create settings modal
    const settingsModal = document.createElement('div');
    settingsModal.id = 'settings-modal';
    settingsModal.style.display = 'none';
    settingsModal.style.position = 'fixed';
    settingsModal.style.top = '50%';
    settingsModal.style.left = '50%';
    settingsModal.style.transform = 'translate(-50%, -50%)';
    settingsModal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    settingsModal.style.color = 'white';
    settingsModal.style.padding = '20px';
    settingsModal.style.borderRadius = '10px';
    settingsModal.style.zIndex = '2000';
    settingsModal.style.minWidth = '300px';
    
    // Create settings content
    const settingsContent = document.createElement('div');
    settingsContent.innerHTML = `
        <h2 style="text-align: center; margin-top: 0;">${getText('ui_settings')}</h2>
        
        <div style="margin-bottom: 20px;">
            <h3>${getText('ui_language')}</h3>
            <select id="settings-language" style="width: 100%; background-color: rgba(0, 0, 0, 0.7); color: white; border: 1px solid #666; padding: 5px; border-radius: 3px;">
                ${Object.entries(Languages).map(([code, name]) => 
                    `<option value="${code}" ${code === settings.language ? 'selected' : ''}>${name}</option>`
                ).join('')}
            </select>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3>${getText('ui_volume')}</h3>
            
            <div style="margin-bottom: 10px;">
                <label for="master-volume" style="display: inline-block; width: 150px;">${getText('ui_master_volume')}</label>
                <input type="range" id="master-volume" min="0" max="1" step="0.01" value="${settings.masterVolume}" style="width: 120px;">
                <span id="master-volume-value">${Math.round(settings.masterVolume * 100)}%</span>
            </div>
            
            <div style="margin-bottom: 10px;">
                <label for="music-volume" style="display: inline-block; width: 150px;">${getText('ui_music_volume')}</label>
                <input type="range" id="music-volume" min="0" max="1" step="0.01" value="${settings.musicVolume}" style="width: 120px;">
                <span id="music-volume-value">${Math.round(settings.musicVolume * 100)}%</span>
            </div>
            
            <div style="margin-bottom: 10px;">
                <label for="sfx-volume" style="display: inline-block; width: 150px;">${getText('ui_sfx_volume')}</label>
                <input type="range" id="sfx-volume" min="0" max="1" step="0.01" value="${settings.sfxVolume}" style="width: 120px;">
                <span id="sfx-volume-value">${Math.round(settings.sfxVolume * 100)}%</span>
            </div>
            
            <div style="margin-bottom: 10px;">
                <label for="weather-volume" style="display: inline-block; width: 150px;">${getText('ui_weather_volume')}</label>
                <input type="range" id="weather-volume" min="0" max="1" step="0.01" value="${settings.weatherSoundVolume}" style="width: 120px;">
                <span id="weather-volume-value">${Math.round(settings.weatherSoundVolume * 100)}%</span>
            </div>
            
            <div style="margin-bottom: 10px;">
                <label for="ui-volume" style="display: inline-block; width: 150px;">${getText('ui_ui_volume')}</label>
                <input type="range" id="ui-volume" min="0" max="1" step="0.01" value="${settings.uiSoundVolume}" style="width: 120px;">
                <span id="ui-volume-value">${Math.round(settings.uiSoundVolume * 100)}%</span>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button id="settings-close" style="background-color: rgba(0, 0, 0, 0.7); color: white; border: 1px solid #666; padding: 8px 15px; border-radius: 5px; cursor: pointer;">${getText('ui_close')}</button>
        </div>
    `;
    
    settingsModal.appendChild(settingsContent);
    
    // Add event listeners
    settingsButton.addEventListener('click', () => {
        settingsModal.style.display = 'block';
        // Resume audio context if it was suspended
        if (audioContext && audioContext.state === 'suspended') {
            audioContext.resume();
        }
    });
    
    // Close button event
    settingsModal.querySelector('#settings-close').addEventListener('click', () => {
        settingsModal.style.display = 'none';
    });
    
    // Language change event
    settingsModal.querySelector('#settings-language').addEventListener('change', (e) => {
        const newLanguage = e.target.value;
        setLanguage(newLanguage);
        settings.language = newLanguage;
        saveSettings();
        
        // Force page reload to ensure all components initialize with the new language
        window.location.reload();
        
        // Note: The following code won't execute due to page reload
        // but keeping it as fallback in case reload is prevented
        updateSettingsUI();
        // settingsButton.title = getText('ui_settings');
    });
    
    // Volume change events
    settingsModal.querySelector('#master-volume').addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        setVolume('masterVolume', value);
        settingsModal.querySelector('#master-volume-value').textContent = `${Math.round(value * 100)}%`;
    });
    
    settingsModal.querySelector('#music-volume').addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        setVolume('musicVolume', value);
        settingsModal.querySelector('#music-volume-value').textContent = `${Math.round(value * 100)}%`;
    });
    
    settingsModal.querySelector('#sfx-volume').addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        setVolume('sfxVolume', value);
        settingsModal.querySelector('#sfx-volume-value').textContent = `${Math.round(value * 100)}%`;
    });
    
    settingsModal.querySelector('#weather-volume').addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        setVolume('weatherSoundVolume', value);
        settingsModal.querySelector('#weather-volume-value').textContent = `${Math.round(value * 100)}%`;
    });
    
    settingsModal.querySelector('#ui-volume').addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        setVolume('uiSoundVolume', value);
        settingsModal.querySelector('#ui-volume-value').textContent = `${Math.round(value * 100)}%`;
    });
    
    // Add modal to document
    document.body.appendChild(settingsModal);
    
    
    return { settingsButton, settingsModal };
}

// Update settings UI with current language
function updateSettingsUI() {
    const settingsModal = document.getElementById('settings-modal');
    if (!settingsModal) return;
    
    // Update title
    const title = settingsModal.querySelector('h2');
    if (title) title.textContent = getText('ui_settings');
    
    // Update language section
    const languageHeader = settingsModal.querySelector('h3:nth-of-type(1)');
    if (languageHeader) languageHeader.textContent = getText('ui_language');
    
    // Update volume section
    const volumeHeader = settingsModal.querySelector('h3:nth-of-type(2)');
    if (volumeHeader) volumeHeader.textContent = getText('ui_volume');
    
    // Update volume labels
    const masterVolumeLabel = settingsModal.querySelector('label[for="master-volume"]');
    if (masterVolumeLabel) masterVolumeLabel.textContent = getText('ui_master_volume');
    
    const musicVolumeLabel = settingsModal.querySelector('label[for="music-volume"]');
    if (musicVolumeLabel) musicVolumeLabel.textContent = getText('ui_music_volume');
    
    const sfxVolumeLabel = settingsModal.querySelector('label[for="sfx-volume"]');
    if (sfxVolumeLabel) sfxVolumeLabel.textContent = getText('ui_sfx_volume');
    
    const weatherVolumeLabel = settingsModal.querySelector('label[for="weather-volume"]');
    if (weatherVolumeLabel) weatherVolumeLabel.textContent = getText('ui_weather_volume');
    
    const uiVolumeLabel = settingsModal.querySelector('label[for="ui-volume"]');
    if (uiVolumeLabel) uiVolumeLabel.textContent = getText('ui_ui_volume');
    
    // Update close button
    const closeButton = settingsModal.querySelector('#settings-close');
    if (closeButton) closeButton.textContent = getText('ui_close');
}