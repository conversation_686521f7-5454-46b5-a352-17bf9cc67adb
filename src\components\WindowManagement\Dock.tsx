import React from 'react';
import { motion } from 'framer-motion';
import { useWindowStore, WindowType } from '../../stores/windowStore';

interface DockItemProps {
  icon: string;
  label: string;
  windowType: WindowType;
  isOpen: boolean;
  onClick: () => void;
}

const DockItem: React.FC<DockItemProps> = ({ icon, label, isOpen, onClick }) => {
  return (
    <motion.div
      className={`dock-item ${isOpen ? 'active' : ''}`}
      whileHover={{ scale: 1.3 }}
      whileTap={{ scale: 0.97 }}
      onClick={onClick}
    >
      <div className="dock-icon">
        <img src={icon} alt={label} />
        {isOpen && <div className="dock-indicator"></div>}
      </div>
      <div className="dock-label">{label}</div>
    </motion.div>
  );
};

export const Dock: React.FC = () => {
  const { windows, toggleWindowVisibility } = useWindowStore();

  const dockItems = [
    {
      icon: 'images/ui_icons/safari.svg',
      label: 'Explore',
      windowType: 'Explore' as WindowType,
      isOpen: windows.Explore.isOpen
    },
    {
      icon: 'images/ui_icons/cube.svg',
      label: 'Inventory',
      windowType: 'Inventory' as WindowType,
      isOpen: windows.Inventory.isOpen
    },
    {
      icon: 'images/ui_icons/tools.svg',
      label: 'Craft',
      windowType: 'Craft' as WindowType,
      isOpen: windows.Craft.isOpen
    },
    {
      icon: 'images/ui_icons/home.svg',
      label: 'Build',
      windowType: 'Ground' as WindowType,
      isOpen: windows.Ground.isOpen
    },
    {
      icon: '/icons/equipment.svg',
      label: 'Equipment',
      windowType: 'Equipment' as WindowType,
      isOpen: windows.Equipment.isOpen
    },
    {
      icon: '/icons/character.svg',
      label: 'Character',
      windowType: 'Character' as WindowType,
      isOpen: windows.Character.isOpen
    },
  ];

  return (
    <motion.div
      className="dock-container"
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ type: 'spring', damping: 20, stiffness: 300, delay: 0.2 }}
    >
      <div className="dock">
        {dockItems.map((item) => (
          <DockItem
            key={item.label}
            icon={item.icon}
            label={item.label}
            windowType={item.windowType}
            isOpen={item.isOpen}
            onClick={() => toggleWindowVisibility(item.windowType)}
          />
        ))}
      </div>
    </motion.div>
  );
};
