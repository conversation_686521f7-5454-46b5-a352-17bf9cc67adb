<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>A Turn Based Survival Game</title>
  <!-- <link rel="shortcut icon" href="/favicon.ico" /> -->
  <!-- <link rel="apple-touch-icon" href="/red%20blob%202d.png" /> -->
  <!-- <link rel="canonical" href="https://www.redblobgames.com/maps/mapgen2/embed.html" /> -->
  <meta name="twitter:creator" content="@redblobgames" />
  <!-- <link rel="stylesheet" href="https://unpkg.com/@radix-ui/themes/styles.css"> -->
  <!-- <link 
    rel="stylesheet" 
    href="node_modules/@radix-ui/themes/styles.css"
  > -->
  <link rel="stylesheet" href="styles/main.css">
  <!-- <link rel="stylesheet" href="build/css/main.css"> -->
  <!-- <link rel="stylesheet" href="build\js\_bundle.css"> -->

  <style>
    /* Weather system styles */
    #weather-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999;
    }
    
    /* Rain animation */
    @keyframes rainFall {
        0% { 
            transform: translateY(-20px);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        95% {
            opacity: 1;
        }
        100% { 
            transform: translateY(100vh);
            opacity: 0;
        }
    }
    
    /* Storm animation */
    @keyframes stormFall {
        0% { 
            transform: translateY(-30px) rotate(-15deg);
            opacity: 0;
        }
        5% {
            opacity: 1;
        }
        95% {
            opacity: 1;
        }
        100% { 
            transform: translateY(100vh) rotate(-15deg);
            opacity: 0;
        }
    }
    
    /* Lightning animation */
    @keyframes lightningFlash {
        0%, 96%, 98%, 100% {
            opacity: 0;
        }
        97%, 97.5% {
            opacity: 0.8;
        }
        99% {
            opacity: 0.4;
        }
    }
    
    /* Fog animation */
    @keyframes fogAnimation {
        0% { opacity: 0; }
        50% { opacity: 1; }
        100% { opacity: 0; }
    }
    
    /* Rain drop styles */
    .rain-drop {
        position: absolute;
        background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(200,230,255,0.8));
        border-radius: 0 0 5px 5px;
        box-shadow: 0 0 5px rgba(200,230,255,0.3);
    }
    
    /* Storm drop styles */
    .storm-drop {
        position: absolute;
        background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(200,230,255,0.8));
        border-radius: 0 0 5px 5px;
        box-shadow: 0 0 8px rgba(200,230,255,0.5);
    }
  </style>

</head>

<body>
  <main>

    <div id="settingsButton">
      <img src = "images/ui_icons/setting.svg" style="width: 70%;height: auto;filter:invert(0.8)"/>
    </div>

    <button id="moveButton">
      Move
    </button>
    
      
    <div id="clockContainer">
      <div id="day-time-display">
        <div id="day-display"></div>
        <div id="time-display"></div>
      </div>
      
      <div id="season-weather-display">
        <div id="season-display" class="weather-display"></div>
        <div id="weatherDisplay"></div>
      </div>

      <div id="time-bar-container">
        <div id="time-bar"></div>
        <div id="time-indicator"></div>
      </div>
    </div>
    
    <div id="statusContainer">
      <div id="statusBar"></div>
      <div id="statusText"></div>
    </div>


    <div  id="bottom-overlay">
      <canvas id="gameCanvas"></canvas>

      <div id="output"></div>
    
      <div id="ui" style="display: none;">
        <div class="row">
        </div>

        <div class="row">
          <div>
            <label>
              Seed:
              <input id="seed" type="number" min="0" placeholder="number" style="width:5em" />
            </label>
            <button onclick="prevSeed();event.preventDefault()" ontouchmove="prevSeed();event.preventDefault()">-</button>
            <button onclick="nextSeed();event.preventDefault()" ontouchmove="nextSeed();event.preventDefault()">+</button>
          </div>
          <div>
            <label>
              Variant:
              <input id="variant" type="number" min="0" max="9" style="width:3em" />
            </label>
            <button onclick="prevVariant();event.preventDefault()"
              ontouchmove="prevVariant();event.preventDefault()">-</button>
            <button onclick="nextVariant();event.preventDefault()"
              ontouchmove="nextVariant();event.preventDefault()">+</button>
          </div>
        </div>

        <div id="group-sliders" class="row">
          <label id="label-rainfall"><span>Dry</span> <input type="range" id="rainfall" list="tickmarks" min="-1" max="1"
              step="0.05"> <span>Wet</span></label>
          <label id="label-north-temperature"><span>N-Cold</span> <input type="range" id="north-temperature"
              list="tickmarks" min="-1.5" max="1.5" step="0.05"> <span>N-Hot</span></label>
          <label id="label-south-temperature"><span>S-Cold</span> <input type="range" id="south-temperature"
              list="tickmarks" min="-1.5" max="1.5" step="0.05"> <span>S-Hot</span></label>
          <label id="label-persistence"><span>Jagged</span> <input type="range" id="persistence" list="tickmarks" min="-1"
              max="1" step="0.05"> <span>Smooth</span></label>
        </div>

        <div id="group-region-count" class="row clickable-labels">
          <span>Number of regions:</span>
          <label><input type="radio" id="size-tiny" name="size" value="tiny"> tiny</label>
          <label><input type="radio" id="size-small" name="size" value="small"> small</label>
          <label><input type="radio" id="size-medium" name="size" value="medium"> medium</label>
          <label><input type="radio" id="size-large" name="size" value="large"> large</label>
          <label><input type="radio" id="size-huge" name="size" value="huge"> huge</label>
          <!-- <label><input type="radio" id="size-ginormous" name="size" value="ginormous"> ginormous</label> -->
        </div>

        <div class="row clickable-labels">
          <span>Rendering:</span>
          <label><input type="checkbox" id="noisy-edges"> noisy edges</label>
          <label><input type="checkbox" id="noisy-fills"> noisy fills</label>
          <label><input type="checkbox" id="icons"> icons</label>
          <label><input type="checkbox" id="biomes"> biomes</label>
          <label><input type="checkbox" id="lighting"> lighting</label>
          <span>Right click to save image</span>
          <div>or <a id="url" target="_top" href="https://www.redblobgames.com/maps/mapgen2/">link to this map</a></div>
        </div>

        <div class="row" style="text-align:center">
          <div><span>from <a rel="author home copyright" href="//www.redblobgames.com/maps/mapgen2/"
                style="text-decoration:none;color:#a44" target="_blank">
                Red Blob Games
              </a></span></div>
        </div>
      </div>

      <!-- <div id="terrainInfoDisplayBottomLeft"></div> -->
    
    </div>

    <div id="react-root"></div>

    <script src="build/js/_bundle.js"></script>

    <datalist id="tickmarks">
      <option value="-0.5" />
      <option value="0" />
      <option value="0.5" />
    </datalist>
  </main>
</body>